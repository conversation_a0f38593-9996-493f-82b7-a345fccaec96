#!/usr/bin/env python3.9
"""
异步功能测试脚本
用于验证 main.py 的异步优化是否正常工作
"""

import asyncio
import aiohttp
import aiofiles
import time
import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_aiohttp():
    """测试 aiohttp 异步 HTTP 请求"""
    print("🔍 测试异步 HTTP 请求...")
    
    try:
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            async with session.get('https://httpbin.org/delay/1') as response:
                if response.status == 200:
                    elapsed = time.time() - start_time
                    print(f"✅ aiohttp 测试成功 - 耗时: {elapsed:.2f}秒")
                    return True
                else:
                    print(f"❌ aiohttp 测试失败 - 状态码: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ aiohttp 测试异常: {str(e)}")
        return False

async def test_aiofiles():
    """测试 aiofiles 异步文件操作"""
    print("🔍 测试异步文件操作...")
    
    try:
        test_file = "test_async_file.tmp"
        test_content = "异步文件测试内容"
        
        # 异步写入
        async with aiofiles.open(test_file, 'w', encoding='utf-8') as f:
            await f.write(test_content)
        
        # 异步读取
        async with aiofiles.open(test_file, 'r', encoding='utf-8') as f:
            content = await f.read()
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.unlink(test_file)
        
        if content == test_content:
            print("✅ aiofiles 测试成功")
            return True
        else:
            print("❌ aiofiles 测试失败 - 内容不匹配")
            return False
            
    except Exception as e:
        print(f"❌ aiofiles 测试异常: {str(e)}")
        return False

async def test_concurrent_requests():
    """测试并发请求性能"""
    print("🔍 测试并发请求性能...")
    
    try:
        async def single_request(session, url):
            async with session.get(url) as response:
                return response.status
        
        urls = ['https://httpbin.org/delay/1'] * 5
        
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            
            # 并发执行
            tasks = [single_request(session, url) for url in urls]
            results = await asyncio.gather(*tasks)
            
            elapsed = time.time() - start_time
            
            success_count = sum(1 for status in results if status == 200)
            
            print(f"✅ 并发测试完成:")
            print(f"   • 请求数量: {len(urls)}")
            print(f"   • 成功数量: {success_count}")
            print(f"   • 总耗时: {elapsed:.2f}秒")
            print(f"   • 平均耗时: {elapsed/len(urls):.2f}秒/请求")
            
            # 如果是串行执行，应该需要约5秒，并发执行应该约1秒
            if elapsed < 2.0:  # 允许一些网络延迟
                print("🚀 并发性能优秀！")
                return True
            else:
                print("⚠️  并发性能一般，可能存在阻塞")
                return True  # 仍然算作成功，可能是网络问题
                
    except Exception as e:
        print(f"❌ 并发测试异常: {str(e)}")
        return False

async def test_main_imports():
    """测试 main.py 的导入和基本功能"""
    print("🔍 测试 main.py 导入...")
    
    try:
        # 尝试导入 main.py 中的异步配置
        from main import async_config, async_http_get
        
        print("✅ main.py 导入成功")
        
        # 测试异步配置
        session = await async_config.get_session()
        if session and not session.closed:
            print("✅ HTTP 会话创建成功")
            await async_config.close_session()
            print("✅ HTTP 会话关闭成功")
            return True
        else:
            print("❌ HTTP 会话创建失败")
            return False
            
    except ImportError as e:
        print(f"❌ main.py 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ main.py 测试异常: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始异步功能测试...\n")
    
    tests = [
        ("aiohttp 异步请求", test_aiohttp),
        ("aiofiles 异步文件", test_aiofiles),
        ("并发请求性能", test_concurrent_requests),
        ("main.py 导入", test_main_imports),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试结果
    print(f"\n{'='*50}")
    print("📊 测试结果汇总")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！异步优化工作正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查依赖安装和网络连接。")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试发生严重错误: {str(e)}")
        sys.exit(1)
