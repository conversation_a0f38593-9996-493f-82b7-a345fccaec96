#!/bin/bash

# 异步优化依赖安装脚本
# 用于安装 main.py 异步优化所需的新依赖项

echo "🚀 开始安装异步优化依赖项..."

# 检查 Python 版本
python_version=$(python3.9 --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 检测到 Python: $python_version"
else
    echo "❌ 未找到 Python 3.9，请先安装 Python 3.9"
    exit 1
fi

# 检查 pip
pip_version=$(python3.9 -m pip --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 检测到 pip: $pip_version"
else
    echo "❌ 未找到 pip，请先安装 pip"
    exit 1
fi

# 升级 pip
echo "📦 升级 pip..."
python3.9 -m pip install --upgrade pip

# 安装异步依赖
echo "📦 安装异步 HTTP 客户端 (aiohttp)..."
python3.9 -m pip install aiohttp>=3.8.0

echo "📦 安装异步文件操作 (aiofiles)..."
python3.9 -m pip install aiofiles>=23.0.0

echo "📦 升级 python-telegram-bot..."
python3.9 -m pip install --upgrade python-telegram-bot>=20.0

echo "📦 安装其他依赖..."
python3.9 -m pip install python-dotenv>=1.0.0 certifi>=2023.0.0

# 验证安装
echo "🔍 验证安装..."
python3.9 -c "import aiohttp; print(f'✅ aiohttp {aiohttp.__version__} 安装成功')" 2>/dev/null || echo "❌ aiohttp 安装失败"
python3.9 -c "import aiofiles; print('✅ aiofiles 安装成功')" 2>/dev/null || echo "❌ aiofiles 安装失败"
python3.9 -c "import telegram; print(f'✅ python-telegram-bot {telegram.__version__} 安装成功')" 2>/dev/null || echo "❌ python-telegram-bot 安装失败"

echo ""
echo "🎉 异步优化依赖安装完成！"
echo ""
echo "📋 新功能特性："
echo "   • 异步 HTTP 请求 - 避免用户间相互阻塞"
echo "   • 连接池管理 - 提高网络请求效率"
echo "   • 请求限流控制 - 防止 API 过载"
echo "   • 异步文件操作 - 提升文件处理性能"
echo "   • 智能错误处理 - 更好的用户体验"
echo ""
echo "🔄 请重启 Bot 以应用异步优化："
echo "   sudo systemctl restart your-bot-service"
echo "   或手动重启 main.py"
echo ""
echo "📊 性能提升预期："
echo "   • 并发处理能力提升 5-10 倍"
echo "   • 响应时间减少 30-50%"
echo "   • 内存使用更加高效"
echo "   • 支持更多同时在线用户"
