#!/bin/bash

# 配置部分
WORK_DIR="/python/Shop Bot"
PID_FILE="${WORK_DIR}/jishou.pid"
LOG_FILE="${WORK_DIR}/monitor.log"
MAIN_SCRIPT="main.py"
OUTPUT_LOG="${WORK_DIR}/jishou.log"

# 设置最大等待时间(秒)
MAX_WAIT=10

# 日志函数
log() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message" | tee -a "$LOG_FILE" >&2
}

# 错误处理函数
die() {
    log "错误: $1"
    exit 1
}

# 切换到工作目录
cd "$WORK_DIR" || die "无法切换到工作目录 $WORK_DIR"

# 检查进程健康状态
check_process_health() {
    local pid=$1
    if [[ -z "$pid" ]]; then
        return 1  # 没有PID，进程不存在
    fi

    # 检查进程是否存在并响应
    if ! kill -0 "$pid" 2>/dev/null; then
        return 1  # 进程不存在或无法访问
    fi

    # 可以添加更多健康检查逻辑
    # 比如检查日志文件更新时间、检查进程CPU使用率等
    return 0  # 进程健康
}

# 检查并终止旧进程
stop_old_process() {
    if [[ -f "$PID_FILE" ]]; then
        local old_pid=$(cat "$PID_FILE")

        if kill -0 "$old_pid" 2>/dev/null; then
            log "发现正在运行的旧进程 PID: $old_pid，正在终止..."

            # 先尝试优雅终止
            kill -TERM "$old_pid"

            # 等待进程结束
            local wait_time=0
            while kill -0 "$old_pid" 2>/dev/null && [[ $wait_time -lt $MAX_WAIT ]]; do
                sleep 1
                ((wait_time++))
            done

            # 如果进程还在运行，强制杀死
            if kill -0 "$old_pid" 2>/dev/null; then
                log "进程未响应TERM信号，强制终止 PID: $old_pid"
                kill -KILL "$old_pid"
                sleep 1
            fi

            log "旧进程已终止"
        else
            log "PID文件存在但进程 $old_pid 已不存在"
        fi

        # 清理PID文件
        rm -f "$PID_FILE" || log "警告: 无法删除PID文件 $PID_FILE"
    fi
}

# 启动新进程
start_new_process() {
    log "启动 ${MAIN_SCRIPT}..."
    
    # 检查主脚本是否存在
    [[ -f "$MAIN_SCRIPT" ]] || die "主脚本 $MAIN_SCRIPT 不存在"
    
    # 启动进程
    nohup python3.9 "$MAIN_SCRIPT" > "$OUTPUT_LOG" 2>&1 &
    local new_pid=$!
    
    # 保存PID
    echo "$new_pid" > "$PID_FILE" || die "无法写入PID文件"
    log "${MAIN_SCRIPT} 已启动，PID: $new_pid"
    
    # 检查进程是否成功启动
    sleep 3
    if kill -0 "$new_pid" 2>/dev/null; then
        log "${MAIN_SCRIPT} 启动成功"
    else
        log "${MAIN_SCRIPT} 启动失败，检查 ${OUTPUT_LOG} 获取详细信息"
        rm -f "$PID_FILE"
        return 1
    fi
    
    return 0
}

# 主执行流程：智能检查和重启
main() {
    local current_pid=""

    # 检查PID文件是否存在
    if [[ -f "$PID_FILE" ]]; then
        current_pid=$(cat "$PID_FILE")
        log "发现PID文件，进程ID: $current_pid"

        # 检查进程是否健康
        if check_process_health "$current_pid"; then
            log "进程 $current_pid 运行正常，无需重启"
            return 0
        else
            log "进程 $current_pid 不健康或已停止，需要重启"
        fi
    else
        log "未找到PID文件，进程可能未运行"
    fi

    # 如果到这里，说明需要重启进程
    log "开始重启进程..."
    stop_old_process
    start_new_process || exit 1

    return 0
}

# 执行主逻辑
main

exit 0